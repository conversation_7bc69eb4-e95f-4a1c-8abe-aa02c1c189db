#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧高速数据中台PDF分析工具
用于分析营运管理第三册需求规格说明书
"""

import os
import sys
import re
from pathlib import Path

def install_dependencies():
    """安装必要的依赖包"""
    try:
        import subprocess
        packages = ['PyPDF2', 'pdfplumber', 'pandas', 'openpyxl']
        for package in packages:
            try:
                __import__(package.lower().replace('-', '_'))
                print(f"✓ {package} 已安装")
            except ImportError:
                print(f"正在安装 {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✓ {package} 安装完成")
    except Exception as e:
        print(f"依赖安装失败: {e}")
        return False
    return True

def extract_pdf_text(pdf_path):
    """提取PDF文本内容"""
    try:
        import PyPDF2
        import pdfplumber
        
        text_content = []
        
        # 使用pdfplumber提取文本（更好的中文支持）
        with pdfplumber.open(pdf_path) as pdf:
            total_pages = len(pdf.pages)
            print(f"PDF总页数: {total_pages}")
            
            for i, page in enumerate(pdf.pages):
                if i % 10 == 0:
                    print(f"正在处理第 {i+1}/{total_pages} 页...")
                
                text = page.extract_text()
                if text:
                    text_content.append({
                        'page': i + 1,
                        'text': text.strip()
                    })
        
        return text_content
    
    except Exception as e:
        print(f"PDF文本提取失败: {e}")
        return None

def analyze_document_structure(text_content):
    """分析文档结构"""
    if not text_content:
        return None
    
    structure = {
        'chapters': [],
        'sections': [],
        'tables': [],
        'figures': [],
        'key_terms': []
    }
    
    # 章节模式匹配
    chapter_pattern = r'第[一二三四五六七八九十\d]+章|第[一二三四五六七八九十\d]+节|^\d+\.\d*\s+'
    table_pattern = r'表\s*\d+[-\.\d]*|表格\s*\d+'
    figure_pattern = r'图\s*\d+[-\.\d]*|图片\s*\d+'
    
    for page_data in text_content:
        page_num = page_data['page']
        text = page_data['text']
        
        # 查找章节
        chapters = re.findall(chapter_pattern, text, re.MULTILINE)
        for chapter in chapters:
            structure['chapters'].append({
                'title': chapter.strip(),
                'page': page_num
            })
        
        # 查找表格
        tables = re.findall(table_pattern, text)
        for table in tables:
            structure['tables'].append({
                'title': table.strip(),
                'page': page_num
            })
        
        # 查找图片
        figures = re.findall(figure_pattern, text)
        for figure in figures:
            structure['figures'].append({
                'title': figure.strip(),
                'page': page_num
            })
    
    return structure

def extract_key_information(text_content):
    """提取关键信息"""
    if not text_content:
        return None
    
    key_info = {
        'system_architecture': [],
        'functional_requirements': [],
        'technical_specifications': [],
        'data_interfaces': [],
        'performance_requirements': []
    }
    
    # 关键词模式
    patterns = {
        'system_architecture': [
            r'系统架构|架构设计|总体架构|技术架构',
            r'微服务|服务架构|分布式|集群',
            r'数据中台|数据平台|大数据平台'
        ],
        'functional_requirements': [
            r'功能需求|业务需求|用户需求',
            r'数据采集|数据处理|数据分析|数据展示',
            r'监控|预警|告警|报表'
        ],
        'technical_specifications': [
            r'技术规范|技术标准|接口规范',
            r'数据库|存储|缓存|消息队列',
            r'API|接口|协议|格式'
        ],
        'data_interfaces': [
            r'数据接口|接口设计|数据交换',
            r'REST|HTTP|WebSocket|TCP',
            r'JSON|XML|CSV|数据格式'
        ],
        'performance_requirements': [
            r'性能要求|性能指标|响应时间',
            r'并发|吞吐量|可用性|稳定性',
            r'扩展性|可维护性|安全性'
        ]
    }
    
    all_text = ' '.join([page['text'] for page in text_content])
    
    for category, pattern_list in patterns.items():
        for pattern in pattern_list:
            matches = re.findall(f'.{{0,50}}{pattern}.{{0,100}}', all_text, re.IGNORECASE)
            key_info[category].extend(matches)
    
    return key_info

def generate_analysis_report(pdf_path, structure, key_info):
    """生成分析报告"""
    report = []
    report.append("# 智慧高速数据中台需求规格说明书分析报告")
    report.append(f"\n**文件**: {os.path.basename(pdf_path)}")
    report.append(f"**分析时间**: {os.path.getctime(pdf_path)}")
    
    # 文档结构分析
    if structure:
        report.append("\n## 1. 文档结构分析")
        
        if structure['chapters']:
            report.append("\n### 1.1 章节结构")
            for i, chapter in enumerate(structure['chapters'][:20]):  # 限制显示前20个
                report.append(f"- {chapter['title']} (第{chapter['page']}页)")
        
        if structure['tables']:
            report.append(f"\n### 1.2 表格统计 (共{len(structure['tables'])}个)")
            for table in structure['tables'][:10]:  # 显示前10个表格
                report.append(f"- {table['title']} (第{table['page']}页)")
        
        if structure['figures']:
            report.append(f"\n### 1.3 图表统计 (共{len(structure['figures'])}个)")
            for figure in structure['figures'][:10]:  # 显示前10个图表
                report.append(f"- {figure['title']} (第{figure['page']}页)")
    
    # 关键信息分析
    if key_info:
        report.append("\n## 2. 关键信息分析")
        
        for category, items in key_info.items():
            if items:
                category_name = {
                    'system_architecture': '系统架构',
                    'functional_requirements': '功能需求',
                    'technical_specifications': '技术规范',
                    'data_interfaces': '数据接口',
                    'performance_requirements': '性能要求'
                }.get(category, category)
                
                report.append(f"\n### 2.{list(key_info.keys()).index(category)+1} {category_name}")
                unique_items = list(set(items))[:10]  # 去重并限制显示数量
                for item in unique_items:
                    report.append(f"- {item.strip()}")
    
    return '\n'.join(report)

def main():
    """主函数"""
    pdf_file = "03-营运管理-第三册 智慧高速数据中台（优化升级-需求规格说明书--全1册.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: 找不到文件 {pdf_file}")
        return
    
    print("开始分析PDF文件...")
    print("1. 检查依赖...")
    if not install_dependencies():
        print("依赖安装失败，无法继续")
        return
    
    print("2. 提取PDF文本...")
    text_content = extract_pdf_text(pdf_file)
    if not text_content:
        print("PDF文本提取失败")
        return
    
    print(f"3. 分析文档结构... (共{len(text_content)}页)")
    structure = analyze_document_structure(text_content)
    
    print("4. 提取关键信息...")
    key_info = extract_key_information(text_content)
    
    print("5. 生成分析报告...")
    report = generate_analysis_report(pdf_file, structure, key_info)
    
    # 保存报告
    report_file = "智慧高速数据中台分析报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✓ 分析完成! 报告已保存到: {report_file}")
    print("\n" + "="*50)
    print(report)

if __name__ == "__main__":
    main()
